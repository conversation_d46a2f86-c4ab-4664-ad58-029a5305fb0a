"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useSelector } from "react-redux";
import { RootState } from "@/store/store";
import {
	Heart,
	Users,
	Globe,
	ArrowRight,
	CheckCircle,
	TrendingUp,
	Shield,
	Star,
	Quote,
	Target,
	Award,
	Sparkles,
} from "lucide-react";
import { useAuth } from "@/hooks/useAuth";

export default function HomePage() {
	const router = useRouter();
	const { isAuthenticated, user } = useSelector(
		(state: RootState) => state.auth
	);
	const { authInitialized } = useAuth();

	useEffect(() => {
		if (isAuthenticated) {
			if (user?.profileCompleted) {
				router.push("/dashboard/home");
			} else {
				router.push("/complete-profile");
			}
		}
	}, [isAuthenticated, user, router]);

	if (!authInitialized) {
		return (
			<div className="min-h-screen flex items-center justify-center bg-gray-50">
				<div className="text-teal-600">Loading...</div>
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-teal-50">
			{/* Hero Section */}
			<div className="relative bg-gradient-to-br from-[#2f8077] via-[#287068] to-[#2c7a72] py-32 overflow-hidden">
				{/* Background Pattern */}
				<div className="absolute inset-0 opacity-10">
					<div
						className="absolute top-0 left-0 w-full h-full"
						style={{
							backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
						}}
					></div>
				</div>

				<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center">
						{/* Logo/Icon */}
						<div className="flex justify-center mb-8">
							<div className="relative">
								<div className="absolute inset-0 bg-white/20 rounded-full blur-xl"></div>
								<div className="relative bg-white/10 backdrop-blur-sm rounded-full p-6 border border-white/20">
									<Heart className="h-16 w-16 text-white" strokeWidth={1.5} />
								</div>
							</div>
						</div>

						{/* Main Heading */}
						<h1 className="text-5xl md:text-7xl font-bold tracking-tight text-white mb-6">
							Make a Difference with{" "}
							<span className="bg-gradient-to-r from-white to-teal-100 bg-clip-text text-transparent">
								GreenGive
							</span>
						</h1>

						{/* Subtitle */}
						<p className="mt-6 max-w-3xl mx-auto text-xl md:text-2xl text-teal-100 leading-relaxed">
							Connect with charitable organizations and support causes that
							matter to you. Every donation creates ripples of positive change
							in our world.
						</p>

						{/* CTA Buttons */}
						<div className="mt-12 flex flex-col sm:flex-row justify-center gap-4 sm:gap-6">
							<Link
								href="/signup"
								className="group relative px-8 py-4 bg-white text-[#287068] font-semibold rounded-xl hover:bg-teal-50 focus:outline-none focus:ring-4 focus:ring-white/30 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl"
								aria-label="Get started with GreenGive"
							>
								<span className="relative z-10 flex items-center justify-center">
									Get Started
									<ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
								</span>
							</Link>
							<Link
								href="/login"
								className="group px-8 py-4 border-2 border-white/30 text-white font-semibold rounded-xl hover:bg-white/10 focus:outline-none focus:ring-4 focus:ring-white/30 transition-all duration-300 backdrop-blur-sm"
								aria-label="Log in to GreenGive"
							>
								Log In
							</Link>
						</div>
					</div>
				</div>
			</div>

			{/* Features Section */}
			<div className="py-16 bg-white">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center">
						<h2 className="text-3xl font-extrabold text-gray-900">
							How It Works
						</h2>
						<p className="mt-4 max-w-2xl mx-auto text-xl text-gray-500">
							GreenGive makes charitable giving simple, transparent, and
							impactful.
						</p>
					</div>

					<div className="mt-12 grid gap-8 grid-cols-1 md:grid-cols-3">
						{/* Feature 1 */}
						<div className="bg-gray-50 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
							<div className="flex justify-center mb-4">
								<Users className="h-12 w-12 text-teal-600" strokeWidth={1.5} />
							</div>
							<div className="text-center">
								<h3 className="text-xl font-medium text-gray-900">
									Create an Account
								</h3>
								<p className="mt-4 text-gray-500">
									Sign up as a donor or organization to start your journey.
								</p>
							</div>
						</div>

						{/* Feature 2 */}
						<div className="bg-gray-50 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
							<div className="flex justify-center mb-4">
								<Globe className="h-12 w-12 text-teal-600" strokeWidth={1.5} />
							</div>
							<div className="text-center">
								<h3 className="text-xl font-medium text-gray-900">
									Complete Your Profile
								</h3>
								<p className="mt-4 text-gray-500">
									Share your story or your organization’s mission.
								</p>
							</div>
						</div>

						{/* Feature 3 */}
						<div className="bg-gray-50 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
							<div className="flex justify-center mb-4">
								<Heart className="h-12 w-12 text-teal-600" strokeWidth={1.5} />
							</div>
							<div className="text-center">
								<h3 className="text-xl font-medium text-gray-900">
									Start Donating
								</h3>
								<p className="mt-4 text-gray-500">
									Discover causes you care about and make an impact.
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Testimonials Section */}
			<div className="py-20 bg-white">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-16">
						<h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
							Stories of Impact
						</h2>
						<p className="text-xl text-gray-600 max-w-3xl mx-auto">
							Hear from our community about the difference we're making together
						</p>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-3 gap-8">
						{[
							{
								quote:
									"GreenGive made it so easy to find and support causes I care about. The transparency is incredible!",
								author: "Sarah Johnson",
								role: "Donor",
								avatar: "SJ",
							},
							{
								quote:
									"As an organization, we've been able to reach more donors and manage donations efficiently through this platform.",
								author: "Michael Chen",
								role: "NGO Director",
								avatar: "MC",
							},
							{
								quote:
									"I love being able to track exactly how my donations are being used. It gives me confidence to give more.",
								author: "Priya Sharma",
								role: "Regular Donor",
								avatar: "PS",
							},
						].map((testimonial, index) => (
							<div
								key={index}
								className="bg-gradient-to-br from-gray-50 to-teal-50 rounded-2xl p-8 border border-gray-100"
							>
								<div className="flex items-center mb-4">
									<Quote
										className="h-8 w-8 text-[#287068] mb-4"
										strokeWidth={1.5}
									/>
								</div>
								<p className="text-gray-700 leading-relaxed mb-6 italic">
									"{testimonial.quote}"
								</p>
								<div className="flex items-center">
									<div className="w-12 h-12 bg-gradient-to-br from-[#2f8077] to-[#287068] rounded-full flex items-center justify-center text-white font-semibold mr-4">
										{testimonial.avatar}
									</div>
									<div>
										<div className="font-semibold text-gray-900">
											{testimonial.author}
										</div>
										<div className="text-gray-600 text-sm">
											{testimonial.role}
										</div>
									</div>
								</div>
							</div>
						))}
					</div>
				</div>
			</div>

			{/* Enhanced CTA Section */}
			<div className="relative bg-gradient-to-br from-[#2f8077] via-[#287068] to-[#2c7a72] py-20 overflow-hidden">
				{/* Background Pattern */}
				<div className="absolute inset-0 opacity-10">
					<div
						className="absolute top-0 left-0 w-full h-full"
						style={{
							backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
						}}
					></div>
				</div>

				<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
					<div className="flex justify-center mb-8">
						<div className="inline-flex items-center justify-center w-20 h-20 bg-white/10 backdrop-blur-sm rounded-full border border-white/20">
							<Sparkles className="h-10 w-10 text-white" strokeWidth={1.5} />
						</div>
					</div>

					<h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
						Ready to Make a Difference?
					</h2>
					<p className="text-xl md:text-2xl text-teal-100 max-w-3xl mx-auto mb-12 leading-relaxed">
						Join thousands of changemakers who are creating positive impact
						every day. Your journey towards making a difference starts here.
					</p>

					<div className="flex flex-col sm:flex-row justify-center gap-4 sm:gap-6">
						<Link
							href="/signup"
							className="group relative px-10 py-5 bg-white text-[#287068] font-bold text-lg rounded-xl hover:bg-teal-50 focus:outline-none focus:ring-4 focus:ring-white/30 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl"
							aria-label="Sign up for GreenGive"
						>
							<span className="relative z-10 flex items-center justify-center">
								Start Your Journey
								<ArrowRight
									className="ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform"
									strokeWidth={2}
								/>
							</span>
						</Link>
						<Link
							href="/login"
							className="group px-10 py-5 border-2 border-white/30 text-white font-bold text-lg rounded-xl hover:bg-white/10 focus:outline-none focus:ring-4 focus:ring-white/30 transition-all duration-300 backdrop-blur-sm"
							aria-label="Log in to GreenGive"
						>
							Welcome Back
						</Link>
					</div>

					{/* Trust Indicators */}
					<div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 opacity-80">
						{[
							{ icon: Shield, text: "Secure & Trusted" },
							{ icon: CheckCircle, text: "Verified Organizations" },
							{ icon: TrendingUp, text: "Real Impact Tracking" },
							{ icon: Award, text: "Transparency Guaranteed" },
						].map((item, index) => (
							<div
								key={index}
								className="flex flex-col items-center text-center"
							>
								<item.icon
									className="h-8 w-8 text-white mb-2"
									strokeWidth={1.5}
								/>
								<span className="text-sm text-teal-100 font-medium">
									{item.text}
								</span>
							</div>
						))}
					</div>
				</div>
			</div>

			{/* Enhanced Footer */}
			<footer className="bg-gray-900 py-16">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
						{/* Brand Section */}
						<div className="md:col-span-2">
							<div className="flex items-center mb-4">
								<div className="w-10 h-10 bg-gradient-to-br from-[#2f8077] to-[#287068] rounded-lg flex items-center justify-center mr-3">
									<Heart className="h-6 w-6 text-white" strokeWidth={1.5} />
								</div>
								<span className="text-2xl font-bold text-white">GreenGive</span>
							</div>
							<p className="text-gray-400 leading-relaxed max-w-md">
								Connecting hearts with causes. Every donation creates ripples of
								positive change in our world. Join us in building a better
								tomorrow, one act of kindness at a time.
							</p>
							<div className="flex space-x-4 mt-6">
								{[
									{ icon: Target, label: "Mission-Driven" },
									{ icon: Shield, label: "Secure Platform" },
									{ icon: Award, label: "Trusted by Thousands" },
								].map((item, index) => (
									<div
										key={index}
										className="flex items-center text-gray-400 text-sm"
									>
										<item.icon className="h-4 w-4 mr-1" strokeWidth={1.5} />
										<span>{item.label}</span>
									</div>
								))}
							</div>
						</div>

						{/* Quick Links */}
						<div>
							<h3 className="text-white font-semibold mb-4">Quick Links</h3>
							<div className="space-y-2">
								{[
									{ name: "About Us", href: "/about" },
									{ name: "How It Works", href: "/how-it-works" },
									{ name: "Success Stories", href: "/stories" },
									{ name: "Contact", href: "/contact" },
								].map((link) => (
									<Link
										key={link.name}
										href={link.href}
										className="block text-gray-400 hover:text-teal-400 transition-colors"
									>
										{link.name}
									</Link>
								))}
							</div>
						</div>

						{/* Legal */}
						<div>
							<h3 className="text-white font-semibold mb-4">Legal</h3>
							<div className="space-y-2">
								{[
									{ name: "Privacy Policy", href: "/privacy" },
									{ name: "Terms of Service", href: "/terms" },
									{ name: "Cookie Policy", href: "/cookies" },
									{ name: "Security", href: "/security" },
								].map((link) => (
									<Link
										key={link.name}
										href={link.href}
										className="block text-gray-400 hover:text-teal-400 transition-colors"
									>
										{link.name}
									</Link>
								))}
							</div>
						</div>
					</div>

					{/* Bottom Section */}
					<div className="border-t border-gray-800 pt-8">
						<div className="flex flex-col md:flex-row justify-between items-center">
							<p className="text-gray-400 text-sm">
								© {new Date().getFullYear()} GreenGive. All rights reserved.
								Made with ❤️ for a better world.
							</p>
							<div className="flex items-center space-x-6 mt-4 md:mt-0">
								<span className="text-gray-400 text-sm">
									Powered by compassion
								</span>
								<div className="flex items-center space-x-2">
									<div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
									<span className="text-green-500 text-sm font-medium">
										Platform Active
									</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</footer>
		</div>
	);
}
